using UnityEngine;
using UnityEngine.AI;
using System.Collections;

namespace HELLSTRIKE
{
    public class ChaserEnemy : MonoBehaviour
    {
        [Header("Detection Settings")]
        [SerializeField] private float detectionRadius = 15f;
        [SerializeField] private float attackRange = 2f;
        [SerializeField] private LayerMask playerLayer;
        
        [Header("Movement")]
        [SerializeField] private float moveSpeed = 4f;
        [SerializeField] private float rotationSpeed = 8f;
        
        [Header("Attack Settings")]
        [SerializeField] private float attackDamage = 25f;
        [SerializeField] private float attackCooldown = 1.5f;
        [SerializeField] private float attackDuration = 0.5f;
        
        [Header("Audio Settings")]
        [SerializeField] private AudioClip attackSound;
        [SerializeField] [Range(0f, 1f)] private float attackSoundVolume = 0.8f;
        [SerializeField] private AudioClip walkingSound;
        [SerializeField] [Range(0f, 1f)] private float walkingSoundVolume = 0.6f;
        [SerializeField] private AudioClip deathSound;
        [SerializeField] [Range(0f, 1f)] private float deathSoundVolume = 0.9f;

        [Header("Health Settings")]
        [SerializeField] private float maxHealth = 100f;
        [SerializeField] private float destroyDelay = 2f;

        private Transform player;
        private NavMeshAgent agent;
        private Animator animator;
        private AudioSource audioSource;
        private AudioSource walkingAudioSource;
        private bool isChasing = false;
        private bool isAttacking = false;
        private float lastAttackTime = 0f;
        private float currentHealth;
        private bool isDead = false;
        private BloodSystem bloodSystem;
        
        private void Awake()
        {
            agent = GetComponent<NavMeshAgent>();
            animator = GetComponent<Animator>();
            audioSource = GetComponent<AudioSource>();

            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();

            // Create a second audio source for walking sounds
            walkingAudioSource = gameObject.AddComponent<AudioSource>();
            walkingAudioSource.loop = true;
            walkingAudioSource.playOnAwake = false;

            if (agent == null)
            {
                agent = gameObject.AddComponent<NavMeshAgent>();
                Debug.Log("ChaserEnemy: Added NavMeshAgent component");
            }

            Debug.Log($"ChaserEnemy: Awake completed. Agent: {agent != null}, Animator: {animator != null}");
        }
        
        private void Start()
        {
            // Initialize health
            currentHealth = maxHealth;

            player = GameObject.FindGameObjectWithTag("Player")?.transform;

            if (player == null)
            {
                Debug.LogError("ChaserEnemy: No GameObject with 'Player' tag found!");
                return;
            }

            Debug.Log($"ChaserEnemy: Found player at {player.position}");

            // Check if NavMeshAgent is properly set up
            if (agent == null)
            {
                Debug.LogError("ChaserEnemy: NavMeshAgent is null!");
                return;
            }

            // Check if enemy is on NavMesh
            if (!agent.isOnNavMesh)
            {
                Debug.LogError($"ChaserEnemy: Not on NavMesh! Position: {transform.position}");
                return;
            }

            // Configure NavMeshAgent
            agent.speed = moveSpeed;
            agent.angularSpeed = rotationSpeed * 100;
            agent.stoppingDistance = attackRange * 0.8f;
            agent.enabled = true;

            Debug.Log($"ChaserEnemy: NavMeshAgent configured. Speed: {agent.speed}, On NavMesh: {agent.isOnNavMesh}");
            Debug.Log($"ChaserEnemy: Detection radius: {detectionRadius}, Attack range: {attackRange}");
            Debug.Log($"ChaserEnemy: Health initialized to {currentHealth}/{maxHealth}");

            // Find BloodSystem
            bloodSystem = FindFirstObjectByType<BloodSystem>();
            if (bloodSystem == null)
            {
                Debug.LogWarning("ChaserEnemy: No BloodSystem found in scene!");
            }

            // Start checking for player
            StartCoroutine(CheckForPlayer());
        }
        
        private IEnumerator CheckForPlayer()
        {
            while (player != null && !isDead)
            {
                float distanceToPlayer = Vector3.Distance(transform.position, player.position);

                if (distanceToPlayer <= detectionRadius)
                {
                    if (!isChasing)
                    {
                        isChasing = true;
                        Debug.Log($"ChaserEnemy: Started chasing player. Distance: {distanceToPlayer:F2}");
                    }

                    // Move toward player if not attacking
                    if (!isAttacking)
                    {
                        agent.SetDestination(player.position);

                        // Start walking animation and sound only when actually moving
                        StartWalkingSound();
                        if (animator != null)
                            animator.SetBool("IsMoving", true);

                        // Debug movement
                        if (agent.pathStatus == UnityEngine.AI.NavMeshPathStatus.PathInvalid)
                        {
                            Debug.LogWarning($"ChaserEnemy: Invalid path to player at {player.position}");
                        }
                        else if (agent.pathStatus == UnityEngine.AI.NavMeshPathStatus.PathPartial)
                        {
                            Debug.LogWarning($"ChaserEnemy: Partial path to player at {player.position}");
                        }
                    }

                    // Check if close enough to attack
                    if (distanceToPlayer <= attackRange && !isAttacking && Time.time >= lastAttackTime + attackCooldown)
                    {
                        Debug.Log($"ChaserEnemy: Attacking! Distance to player: {distanceToPlayer:F2}");
                        StartCoroutine(AttackPlayer());
                    }
                }
                else if (isChasing)
                {
                    isChasing = false;
                    Debug.Log($"ChaserEnemy: Lost player. Distance: {distanceToPlayer:F2}");
                    StopWalkingSound();
                    if (animator != null)
                        animator.SetBool("IsMoving", false);
                    agent.ResetPath();
                }

                yield return new WaitForSeconds(0.2f);
            }
        }
        
        private IEnumerator AttackPlayer()
        {
            if (isAttacking) yield break;

            isAttacking = true;
            lastAttackTime = Time.time;

            // Stop movement and clear path during attack
            agent.isStopped = true;
            agent.ResetPath();
            StopWalkingSound();

            // Play attack animation - ensure IsMoving is false
            if (animator != null)
            {
                animator.SetBool("IsMoving", false);
                animator.SetTrigger("Attack");
            }

            // Play attack sound immediately
            if (attackSound != null && audioSource != null)
            {
                audioSource.clip = attackSound;
                audioSource.volume = attackSoundVolume;
                audioSource.spatialBlend = 1f; // Make it 3D audio
                audioSource.rolloffMode = AudioRolloffMode.Linear;
                audioSource.maxDistance = 30f;
                audioSource.Play();
            }

            // Wait for half the attack duration before dealing damage (for animation timing)
            yield return new WaitForSeconds(attackDuration * 0.5f);

            // Deal damage to player if still in range
            float distanceToPlayer = Vector3.Distance(transform.position, player.position);
            Debug.Log($"ChaserEnemy: Attack damage check - Distance: {distanceToPlayer:F2}, Attack Range: {attackRange}");

            if (distanceToPlayer <= attackRange)
            {
                // Try multiple methods to find the player
                bool playerDamaged = false;

                // Method 1: Use layer mask if set
                if (playerLayer != 0)
                {
                    Collider[] hitColliders = Physics.OverlapSphere(transform.position, attackRange, playerLayer);
                    Debug.Log($"ChaserEnemy: Found {hitColliders.Length} colliders in attack range using layer mask");

                    foreach (var hitCollider in hitColliders)
                    {
                        PlayerHealth playerHealth = hitCollider.GetComponent<PlayerHealth>();
                        if (playerHealth != null)
                        {
                            Debug.Log($"ChaserEnemy: Dealing {attackDamage} damage to {hitCollider.name} via layer mask");
                            playerHealth.TakeDamage(attackDamage);
                            playerDamaged = true;
                        }
                    }
                }

                // Method 2: Direct check on player GameObject if layer mask didn't work
                if (!playerDamaged && player != null)
                {
                    PlayerHealth playerHealth = player.GetComponent<PlayerHealth>();
                    if (playerHealth != null)
                    {
                        Debug.Log($"ChaserEnemy: Dealing {attackDamage} damage to player directly");
                        playerHealth.TakeDamage(attackDamage);
                        playerDamaged = true;
                    }
                }

                if (!playerDamaged)
                {
                    Debug.LogWarning("ChaserEnemy: Player in range but no PlayerHealth component found!");
                }
            }
            else
            {
                Debug.Log("ChaserEnemy: Player moved out of attack range during attack");
            }

            // Wait for the rest of the attack duration
            yield return new WaitForSeconds(attackDuration * 0.5f);

            // Resume movement
            agent.isStopped = false;
            isAttacking = false;

            Debug.Log("ChaserEnemy: Attack completed, resuming normal behavior");
        }
        
        private void StartWalkingSound()
        {
            if (walkingSound != null && walkingAudioSource != null && !walkingAudioSource.isPlaying)
            {
                walkingAudioSource.clip = walkingSound;
                walkingAudioSource.volume = walkingSoundVolume;
                walkingAudioSource.spatialBlend = 1f; // Make it 3D audio
                walkingAudioSource.rolloffMode = AudioRolloffMode.Linear;
                walkingAudioSource.maxDistance = 20f;
                walkingAudioSource.Play();
            }
        }
        
        private void StopWalkingSound()
        {
            if (walkingAudioSource != null && walkingAudioSource.isPlaying)
            {
                walkingAudioSource.Stop();
            }
        }
        
        /// <summary>
        /// Public method to damage the enemy. Call this from weapons/projectiles.
        /// </summary>
        /// <param name="damage">Amount of damage to deal</param>
        public void TakeDamage(float damage)
        {
            if (isDead) return;

            currentHealth -= damage;
            Debug.Log($"ChaserEnemy: Took {damage} damage. Health: {currentHealth}/{maxHealth}");

            // Notify BloodSystem of damage
            if (bloodSystem != null)
            {
                bloodSystem.OnEnemyDamaged(transform.position);
            }

            // Trigger damage animation if available
            if (animator != null)
                animator.SetTrigger("TakeDamage");

            if (currentHealth <= 0)
            {
                Die();
            }
        }

        /// <summary>
        /// Get the current health of the enemy
        /// </summary>
        /// <returns>Current health value</returns>
        public float GetCurrentHealth()
        {
            return currentHealth;
        }

        /// <summary>
        /// Get the maximum health of the enemy
        /// </summary>
        /// <returns>Maximum health value</returns>
        public float GetMaxHealth()
        {
            return maxHealth;
        }

        /// <summary>
        /// Check if the enemy is dead
        /// </summary>
        /// <returns>True if dead, false if alive</returns>
        public bool IsDead()
        {
            return isDead;
        }

        private void Die()
        {
            if (isDead) return;

            isDead = true;
            Debug.Log("ChaserEnemy: Died!");

            // Notify BloodSystem of death
            if (bloodSystem != null)
            {
                bloodSystem.OnEnemyKilled(transform.position);
            }

            // Stop all movement and behavior
            isChasing = false;
            isAttacking = false;
            StopWalkingSound();

            if (agent != null)
            {
                agent.isStopped = true;
                agent.enabled = false;
            }

            // Play death animation
            if (animator != null)
            {
                animator.SetTrigger("Die");
                animator.SetBool("IsMoving", false);
            }

            // Play death sound
            if (deathSound != null && audioSource != null)
            {
                // Create a separate audio source that won't be destroyed with the enemy immediately
                GameObject soundObject = new GameObject("ChaserDeathSound");
                soundObject.transform.position = transform.position;
                AudioSource soundSource = soundObject.AddComponent<AudioSource>();
                soundSource.clip = deathSound;
                soundSource.volume = deathSoundVolume;
                soundSource.spatialBlend = 1f; // Make it 3D audio
                soundSource.rolloffMode = AudioRolloffMode.Linear;
                soundSource.maxDistance = 40f;
                soundSource.Play();

                // Destroy the sound object after the clip finishes playing
                Destroy(soundObject, deathSound.length + 0.1f);
            }

            // Start destruction sequence
            StartCoroutine(DestroyAfterDelay());
        }

        private IEnumerator DestroyAfterDelay()
        {
            // Disable colliders to prevent further interactions
            Collider[] colliders = GetComponentsInChildren<Collider>();
            foreach (var collider in colliders)
            {
                collider.enabled = false;
            }

            yield return new WaitForSeconds(destroyDelay);

            // Optionally fade out mesh renderers before destroying
            MeshRenderer[] renderers = GetComponentsInChildren<MeshRenderer>();
            foreach (var renderer in renderers)
            {
                renderer.enabled = false;
            }

            Destroy(gameObject);
        }

        private void OnDrawGizmosSelected()
        {
            // Visualize detection radius
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, detectionRadius);

            // Visualize attack range
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, attackRange);
        }
    }
}
