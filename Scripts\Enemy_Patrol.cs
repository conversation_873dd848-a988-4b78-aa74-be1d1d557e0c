﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;

namespace HELLSTRIKE
{
    [RequireComponent(typeof(NavMeshAgent))]
    [RequireComponent(typeof(Animator))]
    [RequireComponent(typeof(Rigidbody))]
    [RequireComponent(typeof(Collider))]
    public class Enemy_Patrol : MonoBehaviour
    {
        [Space(10)]
        [Header("Health Settings")]
        [SerializeField] private float maxHealth = 100f;
        private float currentHealth;
        private bool isDead = false;

        [Space(10)]
        [Header("Audio Settings")]
        [SerializeField] private AudioClip attackSound;
        [SerializeField] [Range(0f, 1f)] private float attackSoundVolume = 1f;
        [SerializeField] private AudioClip walkingSound;
        [SerializeField] [Range(0f, 1f)] private float walkingSoundVolume = 0.5f;

        private AudioSource attackAudioSource;
        private AudioSource walkingAudioSource;

        NavMeshAgent agent;
        Animator animator;
        Rigidbody rb;
        BloodSystem bloodSystem;

    // Populates path
    // - Manual: PathNodes must be manually added to List
    // - AutoMagical: PathNodes are added based on how they were added to Scene
    public enum PathGenerator { Manual, AutoMagical };

    [Space(10)]
    [Header("Patrol Settings")]
    [Tooltip("Populates path. Manual: PathNodes are manually added to List. AutoMagical: PathNodes are added based on Path Node Name.")]
    public PathGenerator pathType;

    [Tooltip("Tag to look for when creating the path AutoMagically.")]
    public string pathNodeName = "PathNode";

    // Used to use different PathNode following
    // - Unchecked: Uses Trigger Boxes on nodes
    // - Checked: Uses distance between nodes and "Enemy_Patrol"
    enum Direction { Distance, Trigger };
    Direction AIType = Direction.Distance;

    [Tooltip("Distance to PathNode when Enemy_Patrol chooses next PathNode")]
    public float distanceToNextNode = 1.0f;

    [Tooltip("Holds PathNodes for Enemy to patrol. Must be added if Manual Pathgeneration is selected.")]
    public GameObject[] patrolPath;

    // Keeps track of what PathNode is being used in patrolPath
    int patrolPathIndex = 0;

    [Tooltip("Used to make Enemy_Patrol wait at a PathNode before moving on.")]
    public float waitTime = 1.0f;
    bool isWaiting;

    // Used to check if "Enemy_Patrol" is chasing or path following
    bool isChasing = false;

    [Space(10)]
    [Header("Attack Settings")]
    [Tooltip("Target the AI should move towards. Can be anything in the Scene. ")]
    public GameObject target;   // Where agent should go

    [Tooltip("Detection radius for finding the player")]
    public float detectionRadius = 10f;

    [Tooltip("Attack range - how close enemy needs to be to attack")]
    public float attackRange = 2f;

    [Tooltip("Damage amount (positive number) if Enemy attacks the Player.")]
    public int damageAmount;

    [Tooltip("Damage delay time before Enemy can attack again.")]
    public float damageDelay;

    [Tooltip("Damage distance to apply damage to Player.")]
    public float damageDistance;

    float timeSinceLastDamage;
    private Transform player;
    private bool isAttacking = false;

    [Space(10)]
    [Header("Projectile Settings")]
    [Tooltip("Prefab to spawn for the Projectile being fired.")]
    public Rigidbody projectilePrefab;

    [Tooltip("Empty GameObject to spawn the Projectile in the Scene.")]
    public Transform projectileSpawnPoint;

    [Tooltip("Speed of Projectile being fired.")]
    [Range(5.0f, 15.0f)]
    public float projectileForce;

    // Use this for initialization
    void Start()
    {
        // Set references to NavMeshAgentm, Animator and Rigidbody Component
        // - All should be added to "Enemy_Patrol" GameObject in Inspector
        agent = GetComponent<NavMeshAgent>();
        animator = GetComponent<Animator>();
        rb = GetComponent<Rigidbody>();

        // Sets Rigidbody to work with NavMesh
        rb.isKinematic = true;
        rb.collisionDetectionMode = CollisionDetectionMode.Continuous;

        // Initialize health
        currentHealth = maxHealth;

        // Setup audio sources
        SetupAudioSources();

        // Find the player
        GameObject playerObject = GameObject.FindGameObjectWithTag("Player");
        if (playerObject != null)
        {
            player = playerObject.transform;
            Debug.Log($"Enemy_Patrol: Found player at {player.position}");
        }
        else
        {
            Debug.LogWarning("Enemy_Patrol: No GameObject with 'Player' tag found!");
        }

        // Find BloodSystem
        bloodSystem = FindFirstObjectByType<BloodSystem>();
        if (bloodSystem == null)
        {
            Debug.LogWarning("Enemy_Patrol: No BloodSystem found in scene!");
        }

        // Check if 'PathNode' tag is set
        if (string.IsNullOrEmpty(pathNodeName))
        {
            // Set name to default path name "PathNode"
            pathNodeName = "PathNode";

            // Print message to console
            Debug.Log(name + " is following PathNodes tagged as " + pathNodeName);
        }

        // Find PathNode's based on 'pathNodeName' in Scene and adds them to 'patrolPath'
        // - Order depends on how PathNodes are added to Scene
        // - Overwrites patrolPath if PatrolNodes are manually added
        if (pathType == PathGenerator.AutoMagical)
        {
            // Finds all 'PathNode' tagged GameObjects in Scene and adds to patrolPath
            patrolPath = GameObject.FindGameObjectsWithTag(pathNodeName);
        }

        if (damageAmount <= 0)
        {
            // Should be set to something based on 'Character' health
            damageAmount = 5;

            Debug.Log("DamageAmount not set on " + name + ". Defaulting to " + damageAmount, gameObject);
        }

        if (damageDelay <= 0)
        {
            // Should be set to something longer than the death animation length
            damageDelay = 2.5f;

            Debug.Log("DamageDelay not set on " + name + ". Defaulting to " + damageDelay, gameObject);
        }

        if (damageDistance < 1)
        {
            // Should be set to something farther from 'Player'
            damageDistance = 1.5f;

            Debug.Log("DamageDistance not set on " + name + ". Defaulting to " + damageDistance, gameObject);
        }

        // Use PathNode specified by 'patrolPathIndex' variable
        target = patrolPath[patrolPathIndex];

        // Tell "Enemy_Patrol" to move towards 'target'
        agent.SetDestination(target.transform.position);

        // Tell animator to play movement animation using IsMoving parameter
        bool isMoving = agent.velocity.magnitude > 0.1f && !isDead;
        animator.SetBool("IsMoving", isMoving);

        // Start player detection
        if (player != null)
        {
            StartCoroutine(CheckForPlayer());
        }
    }

    // Update is called once per frame
    void Update()
    {
        // Check if using Distance based node movement
        if (!isChasing && !isWaiting && AIType == Direction.Distance)
        {
            // Check if "Enemy_Patrol" is close to 'target' so it can change to next PathNode
            if (agent.remainingDistance < distanceToNextNode)
                // Start wait timer to keep "Enemy_Patrol" at PathNode
                StartCoroutine(WaitToMove());
        }

        // Check if 'target' variable was set before moving towards it
        if (target && !isDead)
        {
            // Tell "Enemy_Patrol" to move towards 'target'
            agent.SetDestination(target.transform.position);
        }

        // Tell animator to play movement animation using IsMoving parameter
        bool isMoving = agent.velocity.magnitude > 0.1f && !isDead;
        animator.SetBool("IsMoving", isMoving);

        // Debug animation values
        if (Time.frameCount % 60 == 0) // Log every 60 frames to avoid spam
        {
            float playerDistance = player != null ? Vector3.Distance(transform.position, player.position) : -1f;
            Debug.Log($"Enemy_Patrol Debug - IsMoving: {isMoving}, Velocity: {agent.velocity.magnitude:F2}, IsChasing: {isChasing}, IsWaiting: {isWaiting}, PlayerDistance: {playerDistance:F2}");
        }

        // Handle walking sound
        if (!isDead && agent.velocity.magnitude > 0.1f)
        {
            PlayWalkingSound();
        }
        else
        {
            StopWalkingSound();
        }
    }

    private IEnumerator CheckForPlayer()
    {
        while (player != null && !isDead)
        {
            float distanceToPlayer = Vector3.Distance(transform.position, player.position);

            // Check if player is within detection radius
            if (distanceToPlayer <= detectionRadius)
            {
                if (!isChasing)
                {
                    isChasing = true;
                    Debug.Log($"Enemy_Patrol: Started chasing player. Distance: {distanceToPlayer:F2}");
                }

                // Set player as target
                target = player.gameObject;

                // Check if close enough to attack
                if (distanceToPlayer <= attackRange && !isAttacking && Time.time >= timeSinceLastDamage + damageDelay)
                {
                    Debug.Log($"Enemy_Patrol: Attacking! Distance to player: {distanceToPlayer:F2}");
                    StartCoroutine(AttackPlayer());
                }
            }
            else if (isChasing)
            {
                // Player is out of range, return to patrol
                isChasing = false;
                Debug.Log($"Enemy_Patrol: Lost player. Distance: {distanceToPlayer:F2}. Returning to patrol.");
                target = patrolPath[patrolPathIndex];
            }

            yield return new WaitForSeconds(0.2f);
        }
    }

    private IEnumerator AttackPlayer()
    {
        if (isAttacking) yield break;

        isAttacking = true;
        timeSinceLastDamage = Time.time;

        // Stop movement during attack
        agent.isStopped = true;
        animator.SetBool("IsMoving", false);
        animator.SetTrigger("Attack");

        // Play attack sound
        PlayAttackSound();

        // Deal damage to player
        if (player != null)
        {
            PlayerHealth playerHealth = player.GetComponent<PlayerHealth>();
            if (playerHealth != null)
            {
                playerHealth.TakeDamage(damageAmount);
                Debug.Log($"Enemy_Patrol: Dealt {damageAmount} damage to player");
            }
        }

        // Wait for attack animation to complete
        yield return new WaitForSeconds(damageDelay);

        // Resume movement
        agent.isStopped = false;
        isAttacking = false;
    }

    IEnumerator WaitToMove()
    {
        // "Enemy_Patrol" is at target and must wait
        isWaiting = true;

        // Wait for 'waitTime' seconds before moving on
        yield return new WaitForSeconds(waitTime);

        // Choose next 'target'
        GoToNextNode();

        // Wait a bit before stopping the wait
        yield return null;

        // "Enemy_Patrol" is no longer waiting
        isWaiting = false;
    }

    void GoToNextNode()
    {
        // Set 'target' to next PathNode in PatrolPath
        patrolPathIndex++;

        // Reset "Enemy_Patrol" to start path over
        patrolPathIndex %= patrolPath.Length;

        // Set 'target' to PathNode specified by 'patrolPathIndex' variable
        target = patrolPath[patrolPathIndex];
    }

    /// <summary>
    /// Public method to damage the enemy. Call this from weapons/projectiles.
    /// </summary>
    /// <param name="damage">Amount of damage to deal</param>
    public void TakeDamage(float damage)
    {
        if (isDead) return;

        currentHealth -= damage;
        Debug.Log($"Enemy_Patrol: Took {damage} damage. Health: {currentHealth}/{maxHealth}");

        // Notify BloodSystem of damage
        if (bloodSystem != null)
        {
            bloodSystem.OnEnemyDamaged(transform.position);
        }

        // Trigger damage animation if available
        if (animator != null)
            animator.SetTrigger("TakeDamage");

        if (currentHealth <= 0)
        {
            Die();
        }
    }

    // Legacy trigger method - now using distance-based detection instead
    // Keeping this for backward compatibility but it's no longer the primary detection method
    private void OnTriggerStay(Collider other)
    {
        // This method is now handled by CheckForPlayer() coroutine
        // Left empty to avoid conflicts with the new detection system
    }


    // 'Attack1' function used to create and fire a projectile
    void Attack1()
    {
        // Prints a message to Console
        Debug.Log(name + ": Pew Pew", gameObject);

        if (projectilePrefab && projectileSpawnPoint)
        {
            // Create a 'Projectile2' to fire
            Rigidbody temp = Instantiate(projectilePrefab, projectileSpawnPoint.position, projectileSpawnPoint.rotation);

            // Add a force to make 'Projectile' move
            temp.AddForce(projectileSpawnPoint.forward * projectileForce, ForceMode.Impulse);

            // Ignore collisions between 'GameObject' and 'Projectile'
            Physics.IgnoreCollision(gameObject.GetComponent<Collider>(),
                temp.GetComponent<Collider>(), true);
        }
        else
        {
            Debug.Log("Missing Projectile or ProjectileSpawnPoint");
        }
    }

    // Legacy trigger method - now using distance-based detection instead
    private void OnTriggerExit(Collider other)
    {
        // This method is now handled by CheckForPlayer() coroutine
        // Left empty to avoid conflicts with the new detection system
    }

        private void Die()
        {
            if (isDead) return;

            // Enemy is dead
            isDead = true;

            // Notify BloodSystem of death
            if (bloodSystem != null)
            {
                bloodSystem.OnEnemyKilled(transform.position);
            }

            // Set 'agent' to stop moving
            agent.isStopped = true;
            agent.enabled = false;

            // Stop walking sound and movement animation
            StopWalkingSound();
            animator.SetBool("IsMoving", false);

            // Prints message to Console
            Debug.Log(name + " Die", gameObject);

            // Tells 'Animator' to change Parameter in Animator to 'Die'
            // - Uses AnimationEvents to remove gameObject from Scene
            // - AnimationEvent must be added to end of Death Animation
            if (animator != null)
                animator.SetTrigger("Die");

            // Fallback destruction in case animation event doesn't work
            // This ensures the enemy always gets destroyed even if animation controller is broken
            StartCoroutine(EnsureDestruction());
        }

        /// <summary>
        /// Ensures the enemy is destroyed even if animation events fail
        /// </summary>
        private IEnumerator EnsureDestruction()
        {
            float destructionTime = 3f; // Wait 3 seconds for death animation
            Debug.Log($"Enemy_Patrol: Will be destroyed in {destructionTime} seconds as fallback");

            yield return new WaitForSeconds(destructionTime);

            if (gameObject != null)
            {
                Debug.Log("Enemy_Patrol: Forcing destruction due to animation event failure");
                Destroy(gameObject);
            }
        }

        private void OnDeathAnimationComplete()
        {
            // Prints message to Console
            Debug.Log(name + " Removed.", gameObject);

            // Removes gameObject from Scene
            Destroy(gameObject);
        }

        private void SetupAudioSources()
        {
            // Create attack audio source
            GameObject attackAudioObject = new GameObject("AttackAudioSource");
            attackAudioObject.transform.SetParent(transform);
            attackAudioSource = attackAudioObject.AddComponent<AudioSource>();
            attackAudioSource.playOnAwake = false;
            attackAudioSource.spatialBlend = 1f; // 3D sound

            // Create walking audio source
            GameObject walkingAudioObject = new GameObject("WalkingAudioSource");
            walkingAudioObject.transform.SetParent(transform);
            walkingAudioSource = walkingAudioObject.AddComponent<AudioSource>();
            walkingAudioSource.playOnAwake = false;
            walkingAudioSource.spatialBlend = 1f; // 3D sound
            walkingAudioSource.loop = true;
        }

        private void PlayAttackSound()
        {
            if (attackSound != null && attackAudioSource != null)
            {
                attackAudioSource.clip = attackSound;
                attackAudioSource.volume = attackSoundVolume;
                attackAudioSource.Play();
            }
        }

        private void PlayWalkingSound()
        {
            if (walkingSound != null && walkingAudioSource != null && !walkingAudioSource.isPlaying)
            {
                walkingAudioSource.clip = walkingSound;
                walkingAudioSource.volume = walkingSoundVolume;
                walkingAudioSource.Play();
            }
        }

        private void StopWalkingSound()
        {
            if (walkingAudioSource != null && walkingAudioSource.isPlaying)
            {
                walkingAudioSource.Stop();
            }
        }
    }
}
