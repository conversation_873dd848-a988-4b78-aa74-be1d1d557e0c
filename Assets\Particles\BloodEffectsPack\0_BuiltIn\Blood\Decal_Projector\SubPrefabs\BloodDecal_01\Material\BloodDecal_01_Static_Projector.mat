%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: BloodDecal_01_Static_Projector
  m_Shader: {fileID: 4800000, guid: 126db05e954e9ad459993136fea4d7f4, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ISPROJECTOR
  m_InvalidKeywords:
  - _ALPHABLEND_ON
  - _GLOSSYREFLECTIONS_OFF
  - _NORMALMAP
  - _USESPECULARITY_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 1f79c892ca3c44f4997d308c386819da, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FalloffTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: a8c8765927c8b0b4ca3ba567ced8f248, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 2800000, guid: 1f79c892ca3c44f4997d308c386819da, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ProjectorTex:
        m_Texture: {fileID: 2800000, guid: 2b8ea165b42679341b6490631ac7f9c6, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadowTex:
        m_Texture: {fileID: 2800000, guid: 2b8ea165b42679341b6490631ac7f9c6, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlbedoPower: 1.5
    - _AmbientColorIntensity: 2.15
    - _BumpScale: 2
    - _ColorIntensity: 0.39
    - _Columns: 1
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 10
    - _Frame: 0
    - _FrameLength: 1
    - _GlossMapScale: 1
    - _Glossiness: 0.9
    - _GlossyReflections: 0
    - _HueShift: 0
    - _ISPROJECTOR: 1
    - _ISSPRITESHEET: 0
    - _IntenceVal: 100
    - _Metallic: 0
    - _Mode: 2
    - _OcclusionStrength: 1
    - _Opacity: 1
    - _Parallax: 0.02
    - _Rows: 1
    - _Smoothness: 0.8
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 5
    - _UVSec: 0
    - _UseSpecularity: 1
    - _ViewDirMaskThreshold: 0
    - _ZWrite: 0
    m_Colors:
    - _Color: {r: 0.44313726, g: 0.32941177, b: 0.32941177, a: 0.98039216}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
  m_BuildTextureStacks: []
