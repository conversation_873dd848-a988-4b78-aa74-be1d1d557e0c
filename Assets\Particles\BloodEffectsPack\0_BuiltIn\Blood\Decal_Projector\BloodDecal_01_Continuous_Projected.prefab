%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2652885798778331115
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 981631991815134202}
  - component: {fileID: 3200900640869610034}
  m_Layer: 0
  m_Name: BloodDecal_01_Continuous_Projected
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &981631991815134202
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2652885798778331115}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3200900640869610034
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2652885798778331115}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6bba824f6d55e994aabc9d40ac5e0ccd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ignoreLayerMask: 0
  isLoop: 1
  loopingLifetime: Infinity
  loopingLifetimeCounter: 0
  sourcePrefab: {fileID: 5627860478487111644, guid: e781954e8fcf37a4193de8c22288aca1, type: 3}
  originalMat: {fileID: 0}
  lifetime_min: 1
  lifetime_max: 1
  startPosOffset: {x: 0, y: 0, z: 0}
  startPosRandomScale: 0
  startSize_min: 2
  startSize_max: 2
  startRotation_min: -15
  startRotation_max: 15
  frameCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 35
      outSlope: 35
      tangentMode: 34
      weightedMode: 0
      inWeight: 0
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 35
      inSlope: 35
      outSlope: 35
      tangentMode: 34
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  scaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 34
      weightedMode: 0
      inWeight: 0
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 34
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  opacityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 34
      weightedMode: 0
      inWeight: 0
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 0.9310333
      value: 1
      inSlope: -0
      outSlope: -0.24963562
      tangentMode: 5
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.31615943
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: -5.737256
      outSlope: -5.737256
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.13243891
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spawnOptions:
  - currentPrefab: {fileID: 0}
    delay_min: 0
    delay_max: 0
    lifetimeCounter: 0
  - currentPrefab: {fileID: 0}
    delay_min: 0.25
    delay_max: 0.25
    lifetimeCounter: 0
  - currentPrefab: {fileID: 0}
    delay_min: 0.5
    delay_max: 0.5
    lifetimeCounter: 0
  - currentPrefab: {fileID: 0}
    delay_min: 0.75
    delay_max: 0.75
    lifetimeCounter: 0
  - currentPrefab: {fileID: 0}
    delay_min: 1
    delay_max: 1
    lifetimeCounter: 0
