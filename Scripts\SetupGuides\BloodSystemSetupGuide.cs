using UnityEngine;

namespace HELLSTRIKE
{
    /// <summary>
    /// Setup guide for the BloodSystem
    /// This script provides instructions on how to set up the blood healing system
    /// </summary>
    public class BloodSystemSetupGuide : MonoBehaviour
    {
        [Header("Blood System Setup Instructions")]
        [TextArea(10, 20)]
        [SerializeField] private string setupInstructions = @"
BLOOD SYSTEM SETUP GUIDE
========================

The BloodSystem allows players to heal by walking over blood particles that spawn when enemies are killed.

SETUP STEPS:
1. Create an empty GameObject in your scene
2. Name it 'BloodSystem'
3. Add the BloodSystem script component to it
4. Configure the following settings in the inspector:

BLOOD PARTICLE SETTINGS:
- Blood Particle Prefab: Drag your blood particle prefab here
- Blood Lifetime: How long blood particles stay (default: 5 seconds)
- Heal Amount: How much health each blood particle restores (default: 10)
- Blood Spawn Radius: Area around enemy death where blood spawns (default: 2)
- Blood Particle Count: Number of blood particles per enemy death (default: 5)

BLOOD SOUND EFFECTS:
- Blood Sounds (Array of 3): Add 3 different blood sound effects (.mp3 files)
- Blood Sound Volume: Volume for blood sounds (0-1, default: 0.7)

HEALING SETTINGS:
- Heal Radius: Distance player needs to be to collect blood (default: 1.5)
- Player Layer: Set to the layer your player is on
- Enable Healing Feedback: Shows green flash when healing (default: true)
- Healing Flash Color: Color of the healing flash (default: green)
- Healing Flash Duration: How long the flash lasts (default: 0.3 seconds)

BLOOD PARTICLE PREFAB REQUIREMENTS:
Your blood particle prefab should:
- Have a Collider component (can be trigger)
- Have a visual representation (MeshRenderer or ParticleSystem)
- Optionally have physics (Rigidbody) for realistic movement

AUTOMATIC INTEGRATION:
The system automatically integrates with:
- ChaserEnemy
- ExplodingSpider  
- Enemy_Patrol
- RevolverGun
- ShotgunWeapon

When enemies take damage, blood sounds play randomly.
When enemies die, blood particles spawn at their location.
When players walk over blood particles, they heal and the particle is consumed.

TROUBLESHOOTING:
- Make sure your player GameObject has the 'Player' tag
- Ensure the BloodSystem GameObject is active in the scene
- Check that your blood particle prefab is assigned
- Verify that blood sound effects are assigned to the array slots
- Make sure the Player Layer is set correctly in the BloodSystem settings

The system will automatically work with existing weapons and enemies without any additional setup!
";

        [Header("Example Blood Particle Prefab Settings")]
        [TextArea(5, 10)]
        [SerializeField] private string bloodParticlePrefabGuide = @"
BLOOD PARTICLE PREFAB EXAMPLE:
=============================

Create a simple blood particle prefab:

1. Create a new GameObject
2. Name it 'BloodParticle'
3. Add a Sphere or Cube primitive as child
4. Scale it down (e.g., 0.1, 0.1, 0.1)
5. Change material color to red/dark red
6. Add a SphereCollider component
7. Set the collider as trigger (check 'Is Trigger')
8. Optionally add a Rigidbody for physics
9. Save as prefab in your Prefabs folder

The BloodParticle component will be automatically added by the BloodSystem.
";

        private void Start()
        {
            Debug.Log("BloodSystemSetupGuide: Check the inspector for detailed setup instructions!");
        }
    }
}
