using UnityEngine;
using UnityEngine.AI;
using System.Collections;

namespace HELLSTRIKE
{
    public class ExplodingSpider : MonoBehaviour
    {
        [Header("Detection Settings")]
        [SerializeField] private float detectionRadius = 10f;
        [SerializeField] private float explosionRadius = 3f;
        [SerializeField] private LayerMask playerLayer;

        [Header("Movement")]
        [SerializeField] private float moveSpeed = 3.5f;
        [SerializeField] private float rotationSpeed = 5f;

        [Header("Explosion")]
        [SerializeField] private float explosionDamage = 50f;
        [SerializeField] private GameObject explosionEffectPrefab;
        [SerializeField] private AudioClip explosionSound;
        [SerializeField] [Range(0f, 1f)] private float explosionSoundVolume = 1f;
        [SerializeField] private float destroyDelay = 0.2f;

        [Header("Health")]
        [SerializeField] private float maxHealth = 25f;
        [SerializeField] private float currentHealth;

        private Transform player;
        private NavMeshAgent agent;
        private Animator animator;
        private AudioSource audioSource;
        private bool isChasing = false;
        private bool hasExploded = false;
        private bool isDead = false;
        private BloodSystem bloodSystem;
        
        private void Awake()
        {
            agent = GetComponent<NavMeshAgent>();
            animator = GetComponent<Animator>();
            audioSource = GetComponent<AudioSource>();

            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();

            if (agent == null)
            {
                agent = gameObject.AddComponent<NavMeshAgent>();
                Debug.Log("ExplodingSpider: Added NavMeshAgent component");
            }

            Debug.Log($"ExplodingSpider: Awake completed. Agent: {agent != null}, Animator: {animator != null}");
        }
        
        private void Start()
        {
            // Initialize health
            currentHealth = maxHealth;

            player = GameObject.FindGameObjectWithTag("Player")?.transform;

            if (player == null)
            {
                Debug.LogError("ExplodingSpider: No GameObject with 'Player' tag found!");
                return;
            }

            Debug.Log($"ExplodingSpider: Found player at {player.position}");

            // Check if NavMeshAgent is properly set up
            if (agent == null)
            {
                Debug.LogError("ExplodingSpider: NavMeshAgent is null!");
                return;
            }

            // Check if spider is on NavMesh
            if (!agent.isOnNavMesh)
            {
                Debug.LogError($"ExplodingSpider: Not on NavMesh! Position: {transform.position}");
                return;
            }

            // Configure NavMeshAgent
            agent.speed = moveSpeed;
            agent.angularSpeed = rotationSpeed * 100;
            agent.stoppingDistance = explosionRadius * 0.8f;
            agent.enabled = true;

            Debug.Log($"ExplodingSpider: NavMeshAgent configured. Speed: {agent.speed}, On NavMesh: {agent.isOnNavMesh}");
            Debug.Log($"ExplodingSpider: Detection radius: {detectionRadius}, Explosion radius: {explosionRadius}");

            // Find BloodSystem
            bloodSystem = FindFirstObjectByType<BloodSystem>();
            if (bloodSystem == null)
            {
                Debug.LogWarning("ExplodingSpider: No BloodSystem found in scene!");
            }

            // Start checking for player
            StartCoroutine(CheckForPlayer());
        }
        
        private IEnumerator CheckForPlayer()
        {
            while (!hasExploded && player != null)
            {
                float distanceToPlayer = Vector3.Distance(transform.position, player.position);

                if (distanceToPlayer <= detectionRadius)
                {
                    if (!isChasing)
                    {
                        isChasing = true;
                        Debug.Log($"ExplodingSpider: Started chasing player. Distance: {distanceToPlayer:F2}");
                        if (animator != null)
                            animator.SetBool("IsMoving", true);
                    }

                    // Move toward player
                    agent.SetDestination(player.position);

                    // Debug movement
                    if (agent.pathStatus == UnityEngine.AI.NavMeshPathStatus.PathInvalid)
                    {
                        Debug.LogWarning($"ExplodingSpider: Invalid path to player at {player.position}");
                    }
                    else if (agent.pathStatus == UnityEngine.AI.NavMeshPathStatus.PathPartial)
                    {
                        Debug.LogWarning($"ExplodingSpider: Partial path to player at {player.position}");
                    }

                    // Check if close enough to explode
                    if (distanceToPlayer <= explosionRadius)
                    {
                        Debug.Log($"ExplodingSpider: Exploding! Distance to player: {distanceToPlayer:F2}");
                        Explode();
                    }
                }
                else if (isChasing)
                {
                    isChasing = false;
                    Debug.Log($"ExplodingSpider: Lost player. Distance: {distanceToPlayer:F2}");
                    if (animator != null)
                        animator.SetBool("IsMoving", false);
                    agent.ResetPath();
                }

                yield return new WaitForSeconds(0.2f);
            }
        }
        
        private void Explode()
        {
            if (hasExploded) return;

            hasExploded = true;

            // Stop movement
            agent.isStopped = true;
            if (animator != null)
                animator.SetTrigger("Explode");

            // Play explosion effect
            if (explosionEffectPrefab != null)
            {
                Instantiate(explosionEffectPrefab, transform.position, Quaternion.identity);
            }

            // Play sound with volume control - ensure it plays completely
            float soundDuration = 0f;
            if (explosionSound != null)
            {
                // Create a separate audio source that won't be destroyed with the spider
                GameObject soundObject = new GameObject("ExplosionSound");
                soundObject.transform.position = transform.position;
                AudioSource soundSource = soundObject.AddComponent<AudioSource>();
                soundSource.clip = explosionSound;
                soundSource.volume = explosionSoundVolume;
                soundSource.spatialBlend = 1f; // Make it 3D audio
                soundSource.rolloffMode = AudioRolloffMode.Linear;
                soundSource.maxDistance = 50f;
                soundSource.Play();

                soundDuration = explosionSound.length;

                // Destroy the sound object after the clip finishes playing
                Destroy(soundObject, soundDuration + 0.1f); // Small buffer to ensure complete playback
            }

            // Damage player if in range
            Collider[] hitColliders = Physics.OverlapSphere(transform.position, explosionRadius, playerLayer);
            Debug.Log($"ExplodingSpider: Found {hitColliders.Length} colliders in explosion range");

            foreach (var hitCollider in hitColliders)
            {
                PlayerHealth playerHealth = hitCollider.GetComponent<PlayerHealth>();
                if (playerHealth != null)
                {
                    Debug.Log($"ExplodingSpider: Dealing {explosionDamage} damage to {hitCollider.name}");
                    playerHealth.TakeDamage(explosionDamage);
                }
                else
                {
                    Debug.Log($"ExplodingSpider: {hitCollider.name} has no PlayerHealth component");
                }
            }

            // Ensure spider is destroyed after sound finishes or minimum delay, whichever is longer
            float actualDestroyDelay = Mathf.Max(destroyDelay, soundDuration);
            StartCoroutine(DestroyAfterDelay(actualDestroyDelay));
        }
        
        private IEnumerator DestroyAfterDelay(float delay)
        {
            // Disable mesh renderer to make it disappear visually
            MeshRenderer[] renderers = GetComponentsInChildren<MeshRenderer>();
            foreach (var renderer in renderers)
            {
                renderer.enabled = false;
            }

            // Disable colliders to prevent further interactions
            Collider[] colliders = GetComponentsInChildren<Collider>();
            foreach (var collider in colliders)
            {
                collider.enabled = false;
            }

            yield return new WaitForSeconds(delay);
            Destroy(gameObject);
        }

        /// <summary>
        /// Public method to damage the spider. Call this from weapons/projectiles.
        /// </summary>
        /// <param name="damage">Amount of damage to deal</param>
        public void TakeDamage(float damage)
        {
            if (isDead || hasExploded) return;

            currentHealth -= damage;
            Debug.Log($"ExplodingSpider: Took {damage} damage. Health: {currentHealth}/{maxHealth}");

            // Notify BloodSystem of damage
            if (bloodSystem != null)
            {
                bloodSystem.OnEnemyDamaged(transform.position);
            }

            // Trigger damage animation if available
            if (animator != null)
                animator.SetTrigger("TakeDamage");

            if (currentHealth <= 0)
            {
                Die();
            }
        }

        private void Die()
        {
            if (isDead) return;

            isDead = true;
            Debug.Log("ExplodingSpider: Died from damage");

            // Notify BloodSystem of death
            if (bloodSystem != null)
            {
                bloodSystem.OnEnemyKilled(transform.position);
            }

            // Trigger death animation if available
            if (animator != null)
                animator.SetTrigger("Die");

            // Stop movement
            if (agent != null)
            {
                agent.isStopped = true;
                agent.enabled = false;
            }

            // Disable colliders
            Collider[] colliders = GetComponentsInChildren<Collider>();
            foreach (var collider in colliders)
            {
                collider.enabled = false;
            }

            // Destroy after a short delay
            Destroy(gameObject, 2f);
        }

        private void OnDrawGizmosSelected()
        {
            // Visualize detection radius
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, detectionRadius);

            // Visualize explosion radius
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, explosionRadius);
        }
    }
}

